#!/usr/bin/env python3
"""
Test script for the JSON to PDF/DOCX export tool

This script demonstrates how to use the export_jsons_to_formats.py tool
and provides examples for different use cases.
"""

import os
import asyncio
import json
from pathlib import Path
from export_jsons_to_formats import JSONExporter


async def test_export_tool():
    """Test the JSON export functionality"""
    
    print("🧪 Testing JSON Export Tool")
    print("="*50)
    
    # Example parameters (replace with your actual values)
    test_params = {
        "folder_path": "./generated-proposals/proposal_generation_test_20240816_143022",
        "opportunity_id": "TEST_OPP_123",
        "tenant_id": "TEST_TENANT_456"
    }
    
    # Check if test folder exists
    if not os.path.exists(test_params["folder_path"]):
        print(f"❌ Test folder not found: {test_params['folder_path']}")
        print("\n💡 To test this tool:")
        print("1. Run pipeline_6.py to generate a proposal folder")
        print("2. Update the folder_path in this test script")
        print("3. Run this test script again")
        return
    
    # Create exporter instance
    exporter = JSONExporter(
        folder_path=test_params["folder_path"],
        opportunity_id=test_params["opportunity_id"],
        tenant_id=test_params["tenant_id"]
    )
    
    # Test 1: Find JSON files
    print("\n📁 Test 1: Finding JSON files...")
    json_files = exporter.find_json_files()
    
    print(f"Found files:")
    print(f"  - Outlines: {len(json_files['outlines'])}")
    for f in json_files['outlines']:
        print(f"    • {os.path.basename(f)}")
    
    print(f"  - Drafts: {len(json_files['drafts'])}")
    for f in json_files['drafts']:
        print(f"    • {os.path.basename(f)}")
    
    print(f"  - TOCs: {len(json_files['tocs'])}")
    for f in json_files['tocs']:
        print(f"    • {os.path.basename(f)}")
    
    if not any(json_files.values()):
        print("❌ No JSON files found for testing")
        return
    
    # Test 2: Volume number extraction
    print("\n🔢 Test 2: Volume number extraction...")
    test_filenames = [
        "outline_volume_1_test.json",
        "draft_vol_2_test.json", 
        "toc_3_test.json",
        "some_file_without_volume.json"
    ]
    
    for filename in test_filenames:
        volume_num = exporter.extract_volume_number(filename)
        print(f"  {filename} → Volume {volume_num}")
    
    # Test 3: JSON file loading
    print("\n📄 Test 3: JSON file loading...")
    for file_type, files in json_files.items():
        if files:
            test_file = files[0]  # Test first file of each type
            print(f"  Testing {file_type}: {os.path.basename(test_file)}")
            
            data = exporter.load_json_file(test_file)
            if data:
                print(f"    ✅ Loaded successfully")
                if isinstance(data, dict):
                    print(f"    📊 Keys: {list(data.keys())}")
                elif isinstance(data, list):
                    print(f"    📊 List with {len(data)} items")
            else:
                print(f"    ❌ Failed to load")
    
    # Test 4: Export simulation (without actual export)
    print("\n🚀 Test 4: Export simulation...")
    print("This would export the following files:")
    
    for outline_file in json_files['outlines']:
        volume_num = exporter.extract_volume_number(outline_file)
        print(f"  📄 Outline Volume {volume_num}:")
        print(f"    → outline_volume_{volume_num}_{test_params['opportunity_id']}.pdf")
        print(f"    → outline_volume_{volume_num}_{test_params['opportunity_id']}.docx")
    
    for draft_file in json_files['drafts']:
        volume_num = exporter.extract_volume_number(draft_file)
        print(f"  📝 Draft Volume {volume_num}:")
        print(f"    → draft_volume_{volume_num}_{test_params['opportunity_id']}.pdf")
        print(f"    → draft_volume_{volume_num}_{test_params['opportunity_id']}.docx")
    
    print("\n✅ Test completed successfully!")
    print("\n💡 To perform actual export, run:")
    print(f"python3 export_jsons_to_formats.py \\")
    print(f"  --folder {test_params['folder_path']} \\")
    print(f"  --opportunity-id {test_params['opportunity_id']} \\")
    print(f"  --tenant-id {test_params['tenant_id']}")


def create_sample_json_files():
    """Create sample JSON files for testing"""
    print("\n🏗️  Creating sample JSON files for testing...")
    
    # Create test directory
    test_dir = "./test_proposal_export"
    os.makedirs(test_dir, exist_ok=True)
    os.makedirs(os.path.join(test_dir, "outlines"), exist_ok=True)
    os.makedirs(os.path.join(test_dir, "drafts"), exist_ok=True)
    os.makedirs(os.path.join(test_dir, "compliances"), exist_ok=True)
    
    # Sample outline data
    sample_outline = {
        "outlines": [
            {
                "title": "Technical Approach",
                "markdown": "## Technical Approach\n\nOur technical approach focuses on...\n\n**Key Points:**\n- Innovation\n- Quality\n- Efficiency",
                "references": [
                    "IEEE Standard 802.11",
                    "NIST Cybersecurity Framework"
                ]
            },
            {
                "title": "Management Plan", 
                "markdown": "## Management Plan\n\nOur management approach includes...\n\n**Team Structure:**\n- Project Manager\n- Technical Lead\n- Quality Assurance",
                "references": [
                    "PMI Project Management Guide",
                    "Agile Methodology Best Practices"
                ]
            }
        ]
    }
    
    # Sample draft data
    sample_draft = {
        "draft": [
            {
                "title": "1.0 Technical Approach",
                "content": "Our comprehensive technical approach leverages industry best practices..."
            },
            {
                "title": "2.0 Management Plan",
                "content": "Our proven management methodology ensures successful project delivery..."
            }
        ]
    }
    
    # Sample TOC data
    sample_toc = [
        {
            "title": "Technical Approach",
            "number": "1.0",
            "page_limit": 10
        },
        {
            "title": "Management Plan", 
            "number": "2.0",
            "page_limit": 5
        }
    ]
    
    # Save sample files
    files_created = []
    
    # Outline files
    outline_file = os.path.join(test_dir, "outlines", "outline_volume_1_TEST123.json")
    with open(outline_file, 'w') as f:
        json.dump(sample_outline, f, indent=2)
    files_created.append(outline_file)
    
    # Draft files
    draft_file = os.path.join(test_dir, "drafts", "draft_volume_1_TEST123.json")
    with open(draft_file, 'w') as f:
        json.dump(sample_draft, f, indent=2)
    files_created.append(draft_file)
    
    # TOC files
    toc_file = os.path.join(test_dir, "compliances", "toc_volume_1_TEST123.json")
    with open(toc_file, 'w') as f:
        json.dump(sample_toc, f, indent=2)
    files_created.append(toc_file)
    
    print(f"✅ Created {len(files_created)} sample files in {test_dir}")
    for file in files_created:
        print(f"  - {file}")
    
    print(f"\n💡 To test with these sample files, run:")
    print(f"python3 export_jsons_to_formats.py \\")
    print(f"  --folder {test_dir} \\")
    print(f"  --opportunity-id TEST123 \\")
    print(f"  --tenant-id TESTTENANT")
    
    return test_dir


async def main():
    """Main test function"""
    print("🧪 JSON Export Tool Test Suite")
    print("="*60)
    
    choice = input("\nChoose test mode:\n1. Test with existing proposal folder\n2. Create sample files and test\n\nEnter choice (1 or 2): ").strip()
    
    if choice == "1":
        await test_export_tool()
    elif choice == "2":
        test_dir = create_sample_json_files()
        
        # Update test parameters to use sample files
        print(f"\n🧪 Testing with sample files...")
        
        exporter = JSONExporter(
            folder_path=test_dir,
            opportunity_id="TEST123", 
            tenant_id="TESTTENANT"
        )
        
        # Test file detection
        json_files = exporter.find_json_files()
        print(f"\n📁 Sample files detected:")
        print(f"  - Outlines: {len(json_files['outlines'])}")
        print(f"  - Drafts: {len(json_files['drafts'])}")
        print(f"  - TOCs: {len(json_files['tocs'])}")
        
        print(f"\n✅ Sample files ready for testing!")
        print(f"📁 Test directory: {test_dir}")
        
    else:
        print("❌ Invalid choice. Please run again and select 1 or 2.")


if __name__ == "__main__":
    asyncio.run(main())
