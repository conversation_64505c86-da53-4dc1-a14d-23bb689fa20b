# JSON to PDF/DOCX Export Tool

This tool allows you to export existing JSON files (outlines, drafts, TOCs) to PDF and DOCX formats without running the entire pipeline again.

## Features

- ✅ Export outlines to PDF and DOCX
- ✅ Export drafts to PDF and DOCX  
- ✅ Automatic volume detection from filenames
- ✅ TOC integration for proper formatting
- ✅ References handling (for outlines)
- ✅ Interactive and command-line modes
- ✅ Batch processing of multiple volumes
- ✅ Error handling and detailed logging
- ✅ Results summary with export statistics

## Usage

### Interactive Mode (Recommended for first-time users)

```bash
python3 export_jsons_to_formats.py
```

The tool will prompt you for:
- Proposal folder path
- Opportunity ID
- Tenant ID

### Command Line Mode

```bash
# Export both PDF and DOCX
python3 export_jsons_to_formats.py \
  --folder /path/to/proposal/folder \
  --opportunity-id OPID123 \
  --tenant-id TID456

# Export only PDFs
python3 export_jsons_to_formats.py \
  --folder /path/to/proposal/folder \
  --opportunity-id OPID123 \
  --tenant-id TID456 \
  --no-docx

# Export only DOCX
python3 export_jsons_to_formats.py \
  --folder /path/to/proposal/folder \
  --opportunity-id OPID123 \
  --tenant-id TID456 \
  --no-pdf
```

### List Available JSON Files

```bash
python3 export_jsons_to_formats.py --list-files --folder /path/to/proposal/folder
```

## Input Folder Structure

The tool expects a folder structure like this (generated by `pipeline_6.py`):

```
proposal_generation_OPID123_20240816_143022/
├── outlines/
│   ├── outline_volume_1_OPID123.json
│   ├── outline_volume_2_OPID123.json
│   └── ...
├── drafts/
│   ├── draft_volume_1_OPID123.json
│   ├── draft_volume_2_OPID123.json
│   └── ...
├── compliances/
│   ├── toc_volume_1_OPID123.json
│   ├── toc_volume_2_OPID123.json
│   └── ...
├── pdfs/          # Existing PDFs (if any)
└── docx/          # Will be created if doesn't exist
```

## Output Structure

After running the export tool:

```
proposal_generation_OPID123_20240816_143022/
├── pdfs/
│   ├── outline_volume_1_OPID123.pdf
│   ├── outline_volume_2_OPID123.pdf
│   ├── draft_volume_1_OPID123.pdf
│   ├── draft_volume_2_OPID123.pdf
│   └── references_volume_X_OPID123.pdf (if references exist)
├── docx/
│   ├── outline_volume_1_OPID123.docx      ← NEW
│   ├── outline_volume_2_OPID123.docx      ← NEW
│   ├── draft_volume_1_OPID123.docx        ← NEW
│   ├── draft_volume_2_OPID123.docx        ← NEW
│   └── references_volume_X_OPID123.docx   ← NEW (if references exist)
└── export_results_20240816_143022.json    ← Export summary
```

## Command Line Options

| Option | Short | Description |
|--------|-------|-------------|
| `--folder` | `-f` | Path to the proposal folder containing JSON files |
| `--opportunity-id` | `-o` | Opportunity ID for the export |
| `--tenant-id` | `-t` | Tenant ID for the export |
| `--no-pdf` | | Skip PDF export (only export DOCX) |
| `--no-docx` | | Skip DOCX export (only export PDF) |
| `--list-files` | `-l` | List JSON files in the folder and exit |

## File Detection

The tool automatically detects JSON files based on filename patterns:

- **Outlines**: Files containing "outline" in the name
- **Drafts**: Files containing "draft" in the name  
- **TOCs**: Files containing "toc" in the name

Volume numbers are extracted from patterns like:
- `volume_1`, `vol_1`, `_1_`, etc.

## Features

### Automatic TOC Integration
- Automatically finds matching TOC files for each volume
- Applies proper section numbering from TOC data
- Generates professional table of contents in exports

### References Handling
- Extracts references from outline JSON files
- Creates separate reference documents for each volume
- Maintains proper citation formatting

### Error Handling
- Continues processing even if individual files fail
- Detailed error reporting in console and results file
- Graceful handling of missing or malformed JSON files

### Results Summary
- Detailed export statistics
- List of successfully exported files
- Error log for troubleshooting
- Saved as JSON file for record keeping

## Examples

### Basic Export
```bash
python3 export_jsons_to_formats.py \
  --folder ./generated-proposals/proposal_generation_OPID123_20240816_143022 \
  --opportunity-id OPID123 \
  --tenant-id TID456
```

### Check Available Files First
```bash
python3 export_jsons_to_formats.py \
  --list-files \
  --folder ./generated-proposals/proposal_generation_OPID123_20240816_143022
```

### Export Only DOCX Files
```bash
python3 export_jsons_to_formats.py \
  --folder ./generated-proposals/proposal_generation_OPID123_20240816_143022 \
  --opportunity-id OPID123 \
  --tenant-id TID456 \
  --no-pdf
```

## Troubleshooting

### Common Issues

1. **"No JSON files found"**
   - Check folder path is correct
   - Ensure JSON files exist in subfolders
   - Use `--list-files` to see what files are detected

2. **"Database connection error"**
   - Ensure database is running and accessible
   - Check database configuration in the main application

3. **"Export failed"**
   - Check opportunity ID and tenant ID are valid
   - Ensure sufficient disk space for output files
   - Check console output for specific error details

### Getting Help

Run with no arguments for interactive mode:
```bash
python3 export_jsons_to_formats.py
```

Or use the help flag:
```bash
python3 export_jsons_to_formats.py --help
```

## Requirements

- Python 3.7+
- All dependencies from the main AIService application
- Database access (same as main application)
- Sufficient disk space for output files

## Notes

- The tool uses the same export controllers as the main pipeline
- Cover page ID 4344 is used by default (same as pipeline)
- User ID 69 is used for database operations (same as pipeline)
- Export quality and formatting match the main pipeline output
